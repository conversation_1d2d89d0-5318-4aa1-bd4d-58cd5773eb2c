#!/usr/bin/env python3
"""
Test script to check channel access
Run this to verify if your bot can access specific channels
"""

import asyncio
from pyrogram import Client
from config import API_ID, API_HASH, SESSION_NAME

async def test_channel_access():
    """Test if bot can access specific channels"""
    
    # Replace with your problematic channel ID
    test_channel_id = "-1002583341599"
    
    async with Client(SESSION_NAME, api_id=API_ID, api_hash=API_HASH) as app:
        try:
            print(f"Testing access to channel: {test_channel_id}")
            
            # Try to get chat info
            chat = await app.get_chat(test_channel_id)
            print(f"✅ SUCCESS: Can access channel")
            print(f"   Title: {chat.title}")
            print(f"   Type: {chat.type}")
            print(f"   Members: {chat.members_count if hasattr(chat, 'members_count') else 'Unknown'}")
            
            # Try to get recent messages
            try:
                async for message in app.get_chat_history(test_channel_id, limit=1):
                    print(f"   Last message ID: {message.id}")
                    break
            except Exception as e:
                print(f"   ⚠️  Cannot read messages: {e}")
                
        except Exception as e:
            print(f"❌ ERROR: Cannot access channel")
            print(f"   Error: {e}")
            print(f"   Possible solutions:")
            print(f"   1. Add bot to channel as admin/member")
            print(f"   2. Check if channel ID is correct")
            print(f"   3. Use @username for public channels")

if __name__ == "__main__":
    asyncio.run(test_channel_access())
