import sqlite3
import logging
from typing import List, Tu<PERSON>, Optional
from config import DATABASE_PATH

class Database:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.init_database()

    def init_database(self):
        """Ma'lumotlar bazasini yaratish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Source kanallar jadvali
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS source_channels (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        channel_id TEXT UNIQUE NOT NULL,
                        channel_name TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Target kanallar jadvali
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS target_channels (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        channel_id TEXT UNIQUE NOT NULL,
                        channel_name TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Kanallar orasidagi bog'lanish jadvali
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS channel_mappings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_channel_id TEXT NOT NULL,
                        target_channel_id TEXT NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(source_channel_id, target_channel_id)
                    )
                ''')

                # Forward statistikasi jadvali
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forward_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_channel_id TEXT,
                        target_channel_id TEXT,
                        message_id INTEGER,
                        forward_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        success BOOLEAN DEFAULT 1
                    )
                ''')

                # Message settings jadvali
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS message_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_channel_id TEXT NOT NULL UNIQUE,
                        send_mode TEXT DEFAULT 'media_group_with_caption',
                        custom_caption TEXT DEFAULT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Message logs jadvali - barcha kelgan xabarlarni log qilish uchun
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS message_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_channel_id TEXT NOT NULL,
                        source_channel_name TEXT,
                        message_id INTEGER NOT NULL,
                        message_type TEXT NOT NULL,
                        message_text TEXT,
                        has_media BOOLEAN DEFAULT 0,
                        media_type TEXT,
                        media_group_id TEXT,
                        user_id INTEGER,
                        username TEXT,
                        received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        forwarded_to_saved BOOLEAN DEFAULT 0,
                        saved_message_id INTEGER
                    )
                ''')

                # Instagram post statistics jadvali
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS instagram_post_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_channel_id TEXT NOT NULL,
                        message_id INTEGER NOT NULL,
                        success BOOLEAN NOT NULL,
                        posted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        error_message TEXT
                    )
                ''')

                conn.commit()
                logging.info("Ma'lumotlar bazasi muvaffaqiyatli yaratildi")

        except Exception as e:
            logging.error(f"Ma'lumotlar bazasini yaratishda xatolik: {e}")

    def add_source_channel(self, channel_id: str, channel_name: str = None) -> bool:
        """Source kanal qo'shish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT OR REPLACE INTO source_channels (channel_id, channel_name) VALUES (?, ?)",
                    (channel_id, channel_name)
                )
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Source kanal qo'shishda xatolik: {e}")
            return False

    def add_target_channel(self, channel_id: str, channel_name: str = None) -> bool:
        """Target kanal qo'shish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT OR REPLACE INTO target_channels (channel_id, channel_name) VALUES (?, ?)",
                    (channel_id, channel_name)
                )
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Target kanal qo'shishda xatolik: {e}")
            return False

    def get_source_channels(self) -> List[Tuple]:
        """Barcha source kanallarni olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM source_channels WHERE is_active = 1")
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"Source kanallarni olishda xatolik: {e}")
            return []

    def get_target_channels(self) -> List[Tuple]:
        """Barcha target kanallarni olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM target_channels WHERE is_active = 1")
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"Target kanallarni olishda xatolik: {e}")
            return []

    def remove_source_channel(self, channel_id: str) -> bool:
        """Source kanalni o'chirish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("UPDATE source_channels SET is_active = 0 WHERE channel_id = ?", (channel_id,))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Source kanalni o'chirishda xatolik: {e}")
            return False

    def remove_target_channel(self, channel_id: str) -> bool:
        """Target kanalni o'chirish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("UPDATE target_channels SET is_active = 0 WHERE channel_id = ?", (channel_id,))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Target kanalni o'chirishda xatolik: {e}")
            return False

    def add_forward_stat(self, source_channel_id: str, target_channel_id: str, message_id: int, success: bool = True):
        """Forward statistikasini qo'shish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO forward_stats (source_channel_id, target_channel_id, message_id, success) VALUES (?, ?, ?, ?)",
                    (source_channel_id, target_channel_id, message_id, success)
                )
                conn.commit()
        except Exception as e:
            logging.error(f"Forward statistikasini qo'shishda xatolik: {e}")

    def get_stats(self) -> dict:
        """Umumiy statistikani olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Jami forward qilingan xabarlar
                cursor.execute("SELECT COUNT(*) FROM forward_stats WHERE success = 1")
                total_forwards = cursor.fetchone()[0]

                # Muvaffaqiyatsiz forward
                cursor.execute("SELECT COUNT(*) FROM forward_stats WHERE success = 0")
                failed_forwards = cursor.fetchone()[0]

                # Source kanallar soni
                cursor.execute("SELECT COUNT(*) FROM source_channels WHERE is_active = 1")
                source_count = cursor.fetchone()[0]

                # Target kanallar soni
                cursor.execute("SELECT COUNT(*) FROM target_channels WHERE is_active = 1")
                target_count = cursor.fetchone()[0]

                return {
                    'total_forwards': total_forwards,
                    'failed_forwards': failed_forwards,
                    'source_channels': source_count,
                    'target_channels': target_count
                }
        except Exception as e:
            logging.error(f"Statistikani olishda xatolik: {e}")
            return {}

    def add_channel_mapping(self, source_channel_id: str, target_channel_id: str) -> bool:
        """Source va target kanal orasida bog'lanish yaratish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT OR REPLACE INTO channel_mappings (source_channel_id, target_channel_id) VALUES (?, ?)",
                    (source_channel_id, target_channel_id)
                )
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Kanal bog'lanishini qo'shishda xatolik: {e}")
            return False

    def remove_channel_mapping(self, source_channel_id: str, target_channel_id: str) -> bool:
        """Source va target kanal orasidagi bog'lanishni o'chirish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "UPDATE channel_mappings SET is_active = 0 WHERE source_channel_id = ? AND target_channel_id = ?",
                    (source_channel_id, target_channel_id)
                )
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Kanal bog'lanishini o'chirishda xatolik: {e}")
            return False

    def get_target_channels_for_source(self, source_channel_id: str) -> List[Tuple]:
        """Berilgan source kanal uchun target kanallarni olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT tc.* FROM target_channels tc
                    JOIN channel_mappings cm ON tc.channel_id = cm.target_channel_id
                    WHERE cm.source_channel_id = ? AND cm.is_active = 1 AND tc.is_active = 1
                """, (source_channel_id,))
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"Source kanal uchun target kanallarni olishda xatolik: {e}")
            return []

    def get_all_channel_mappings(self) -> List[Tuple]:
        """Barcha kanal bog'lanishlarini olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT
                        sc.channel_id as source_id,
                        sc.channel_name as source_name,
                        tc.channel_id as target_id,
                        tc.channel_name as target_name,
                        cm.created_at
                    FROM channel_mappings cm
                    JOIN source_channels sc ON cm.source_channel_id = sc.channel_id
                    JOIN target_channels tc ON cm.target_channel_id = tc.channel_id
                    WHERE cm.is_active = 1 AND sc.is_active = 1 AND tc.is_active = 1
                    ORDER BY sc.channel_name, tc.channel_name
                """)
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"Kanal bog'lanishlarini olishda xatolik: {e}")
            return []

    def get_source_channels_with_targets(self) -> dict:
        """Har bir source kanal uchun target kanallar ro'yxatini olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Barcha source kanallarni olish
                cursor.execute("SELECT * FROM source_channels WHERE is_active = 1")
                source_channels = cursor.fetchall()

                result = {}
                for source in source_channels:
                    source_id = source[1]  # channel_id
                    source_name = source[2] or source_id  # channel_name yoki channel_id

                    # Bu source uchun target kanallarni olish
                    targets = self.get_target_channels_for_source(source_id)
                    result[source_id] = {
                        'name': source_name,
                        'targets': targets
                    }

                return result
        except Exception as e:
            logging.error(f"Source kanallar va targetlarni olishda xatolik: {e}")
            return {}

    def get_channel_by_id(self, channel_id: str, is_source: bool = True) -> Optional[Tuple]:
        """Kanal ID bo'yicha kanal ma'lumotlarini olish"""
        try:
            table_name = "source_channels" if is_source else "target_channels"
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM {table_name} WHERE channel_id = ? AND is_active = 1", (channel_id,))
                return cursor.fetchone()
        except Exception as e:
            logging.error(f"Kanal ma'lumotlarini olishda xatolik: {e}")
            return None

    def set_message_settings(self, source_channel_id: str, send_mode: str, custom_caption: str = None) -> bool:
        """Source kanal uchun message settings sozlash"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO message_settings
                    (source_channel_id, send_mode, custom_caption, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                """, (source_channel_id, send_mode, custom_caption))
                conn.commit()
                logging.info(f"Message settings sozlandi: {source_channel_id} -> {send_mode}")
                return True
        except Exception as e:
            logging.error(f"Message settings sozlashda xatolik: {e}")
            return False

    def get_message_settings(self, source_channel_id: str) -> Optional[Tuple]:
        """Source kanal uchun message settings olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM message_settings
                    WHERE source_channel_id = ? AND is_active = 1
                """, (source_channel_id,))
                result = cursor.fetchone()
                if result:
                    return result
                else:
                    # Default settings qaytarish
                    return (None, source_channel_id, 'media_group_with_caption', None, 1, None, None)
        except Exception as e:
            logging.error(f"Message settings olishda xatolik: {e}")
            return (None, source_channel_id, 'media_group_with_caption', None, 1, None, None)

    def get_all_message_settings(self) -> List[Tuple]:
        """Barcha message settings olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT ms.*, sc.channel_name
                    FROM message_settings ms
                    JOIN source_channels sc ON ms.source_channel_id = sc.channel_id
                    WHERE ms.is_active = 1 AND sc.is_active = 1
                    ORDER BY sc.channel_name
                """)
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"Barcha message settings olishda xatolik: {e}")
            return []

    def add_message_log(self, source_channel_id: str, source_channel_name: str,
                       message_id: int, message_type: str, message_text: str = None,
                       has_media: bool = False, media_type: str = None,
                       media_group_id: str = None, user_id: int = None,
                       username: str = None) -> bool:
        """Kelgan xabarni log qilish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO message_logs
                    (source_channel_id, source_channel_name, message_id, message_type,
                     message_text, has_media, media_type, media_group_id, user_id, username)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (source_channel_id, source_channel_name, message_id, message_type,
                      message_text, has_media, media_type, media_group_id, user_id, username))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Message log qo'shishda xatolik: {e}")
            return False

    def update_message_log_saved_status(self, log_id: int, saved_message_id: int) -> bool:
        """Message log ning saved messages ga yuborilganligini belgilash"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE message_logs
                    SET forwarded_to_saved = 1, saved_message_id = ?
                    WHERE id = ?
                """, (saved_message_id, log_id))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Message log saved status yangilashda xatolik: {e}")
            return False

    def get_message_logs(self, limit: int = 100, source_channel_id: str = None) -> List[Tuple]:
        """Message loglarni olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if source_channel_id:
                    cursor.execute("""
                        SELECT * FROM message_logs
                        WHERE source_channel_id = ?
                        ORDER BY received_at DESC
                        LIMIT ?
                    """, (source_channel_id, limit))
                else:
                    cursor.execute("""
                        SELECT * FROM message_logs
                        ORDER BY received_at DESC
                        LIMIT ?
                    """, (limit,))
                return cursor.fetchall()
        except Exception as e:
            logging.error(f"Message loglarni olishda xatolik: {e}")
            return []

    def get_message_logs_stats(self) -> dict:
        """Message logs statistikasini olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Jami log qilingan xabarlar
                cursor.execute("SELECT COUNT(*) FROM message_logs")
                total_logs = cursor.fetchone()[0]

                # Saved messages ga yuborilgan xabarlar
                cursor.execute("SELECT COUNT(*) FROM message_logs WHERE forwarded_to_saved = 1")
                forwarded_to_saved = cursor.fetchone()[0]

                # Oxirgi 24 soatdagi xabarlar
                cursor.execute("""
                    SELECT COUNT(*) FROM message_logs
                    WHERE received_at >= datetime('now', '-1 day')
                """)
                last_24h = cursor.fetchone()[0]

                return {
                    'total_logs': total_logs,
                    'forwarded_to_saved': forwarded_to_saved,
                    'last_24h': last_24h
                }
        except Exception as e:
            logging.error(f"Message logs statistikasini olishda xatolik: {e}")
            return {}

    def add_instagram_post_stat(self, source_channel_id: str, message_id: int, success: bool, error_message: str = None) -> bool:
        """Instagram post statistikasini qo'shish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO instagram_post_stats
                    (source_channel_id, message_id, success, error_message)
                    VALUES (?, ?, ?, ?)
                """, (source_channel_id, message_id, success, error_message))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Instagram post statistikasini qo'shishda xatolik: {e}")
            return False

    def get_instagram_stats(self) -> dict:
        """Instagram post statistikasini olish"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Jami Instagram postlar
                cursor.execute("SELECT COUNT(*) FROM instagram_post_stats")
                total_posts = cursor.fetchone()[0]

                # Muvaffaqiyatli postlar
                cursor.execute("SELECT COUNT(*) FROM instagram_post_stats WHERE success = 1")
                successful_posts = cursor.fetchone()[0]

                # Muvaffaqiyatsiz postlar
                cursor.execute("SELECT COUNT(*) FROM instagram_post_stats WHERE success = 0")
                failed_posts = cursor.fetchone()[0]

                # Oxirgi 24 soatdagi postlar
                cursor.execute("""
                    SELECT COUNT(*) FROM instagram_post_stats
                    WHERE posted_at >= datetime('now', '-1 day')
                """)
                last_24h_posts = cursor.fetchone()[0]

                return {
                    'total_posts': total_posts,
                    'successful_posts': successful_posts,
                    'failed_posts': failed_posts,
                    'last_24h_posts': last_24h_posts,
                    'success_rate': round((successful_posts / total_posts * 100), 2) if total_posts > 0 else 0
                }
        except Exception as e:
            logging.error(f"Instagram statistikasini olishda xatolik: {e}")
            return {}
