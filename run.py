#!/usr/bin/env python3
"""
Channel Forwarder Bot - Ishga tushirish skripti
"""
import sys
import logging
from main import ChannelForwarderUserBot


def check_config():
    """Konfiguratsiyani tekshirish"""
    from config import API_ID, API_HASH, ADMIN_USER_ID

    if not API_ID or API_ID == 0:
        print("❌ Xatolik: API_ID .env faylida to'g'ri sozlanmagan!")
        print("1. my.telegram.org ga boring")
        print("2. API development tools bo'limiga kiring")
        print("3. API_ID ni .env fayliga kiriting")
        return False

    if not API_HASH or API_HASH == 'your_api_hash_here':
        print("❌ Xatolik: API_HASH .env faylida to'g'ri sozlanmagan!")
        print("1. my.telegram.org ga boring")
        print("2. API development tools bo'limiga kiring")
        print("3. API_HASH ni .env fayliga kiriting")
        return False

    if not ADMIN_USER_ID or ADMIN_USER_ID == 0:
        print("❌ Xatolik: ADMIN_USER_ID .env faylida to'g'ri sozlanmagan!")
        print("1. .env faylini oching")
        print("2. ADMIN_USER_ID=your_telegram_user_id ni o'zgartiring")
        return False

    return True


def main():
    """Asosiy funksiya"""
    print("🤖 Channel Forwarder User Bot ishga tushirilmoqda...")

    # Konfiguratsiyani tekshirish
    if not check_config():
        sys.exit(1)

    try:
        # User botni yaratish va ishga tushirish
        bot = ChannelForwarderUserBot()
        print("✅ User bot muvaffaqiyatli ishga tushirildi!")
        print("🌐 Web admin panel: http://localhost:5000")
        print("📱 O'zingizga /start komandasi yuboring")
        print("⚠️  Birinchi marta ishga tushirganda telefon raqamingizni tasdiqlash kerak bo'ladi")
        print("⏹️  To'xtatish uchun Ctrl+C bosing")

        bot.run()

    except KeyboardInterrupt:
        print("\n⏹️  User bot to'xtatildi")
    except Exception as e:
        print(f"❌ Xatolik: {e}")
        logging.error(f"User bot ishga tushirishda xatolik: {e}")


if __name__ == "__main__":
    main()
